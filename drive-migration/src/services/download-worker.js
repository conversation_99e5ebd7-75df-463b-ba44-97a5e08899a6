/**
 * Download Worker
 * Worker x<PERSON> lý download đồng thời từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import { createWriteStream } from 'fs';
import EventEmitter from 'events';
import { googleDriveAPI } from '../api/google-drive-api.js';

export class DownloadWorker extends EventEmitter {
    constructor(sessionId, sessionConfig, supabase) {
        super();
        this.sessionId = sessionId;
        this.sessionConfig = sessionConfig;
        this.supabase = supabase;
        this.isRunning = false;
        this.isPaused = false;
        this.isCancelled = false;
        this.activeDownloads = new Map(); // fileId -> download info
        this.stats = {
            downloadedFiles: 0,
            failedFiles: 0,
            skippedFiles: 0,
            downloadedSize: 0
        };
    }

    /**
     * Bắt đầu download process
     */
    async start() {
        if (this.isRunning) {
            throw new Error('Worker is already running');
        }

        this.isRunning = true;
        this.isPaused = false;
        this.isCancelled = false;

        try {
            console.log(`🚀 Starting download worker for session: ${this.sessionId}`);

            // L<PERSON>y danh sách files cần download
            const { data: downloadItems, error } = await this.supabase.getServiceClient()
                .from('download_items')
                .select('*')
                .eq('download_session_id', this.sessionId)
                .eq('status', 'pending')
                .order('created_at');

            if (error) {
                throw new Error(`Failed to get download items: ${error.message}`);
            }

            if (downloadItems.length === 0) {
                console.log('No files to download');
                await this.completeSession();
                return;
            }

            console.log(`📋 Found ${downloadItems.length} files to download`);

            // Tạo thư mục download path nếu chưa tồn tại
            if (!fs.existsSync(this.sessionConfig.download_path)) {
                fs.mkdirSync(this.sessionConfig.download_path, { recursive: true });
            }

            // Xử lý download với concurrency
            await this.processDownloads(downloadItems);

        } catch (error) {
            console.error('❌ Error in download worker:', error.message);
            await this.handleWorkerError(error);
            this.emit('error', error);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Xử lý downloads với concurrency control
     */
    async processDownloads(downloadItems) {
        const concurrentDownloads = this.sessionConfig.concurrent_downloads || 3;
        const queue = [...downloadItems];
        const activePromises = [];

        while (queue.length > 0 || activePromises.length > 0) {
            // Kiểm tra pause/cancel
            if (this.isPaused) {
                console.log('⏸️ Download paused, waiting...');
                await this.waitForResume();
            }

            if (this.isCancelled) {
                console.log('❌ Download cancelled');
                break;
            }

            // Bắt đầu downloads mới nếu có slot
            while (activePromises.length < concurrentDownloads && queue.length > 0) {
                const item = queue.shift();
                const promise = this.downloadFile(item)
                    .then(() => {
                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    })
                    .catch((error) => {
                        console.error(`Error downloading ${item.file_name}:`, error.message);
                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    });

                activePromises.push(promise);
            }

            // Chờ ít nhất 1 download hoàn thành
            if (activePromises.length > 0) {
                await Promise.race(activePromises);
            }

            // Emit progress
            await this.emitProgress();
        }

        // Chờ tất cả downloads hoàn thành
        await Promise.all(activePromises);

        if (!this.isCancelled) {
            await this.completeSession();
        }
    }

    /**
     * Download một file
     */
    async downloadFile(downloadItem) {
        const startTime = Date.now();

        try {
            // Update status to downloading
            await this.updateDownloadItemStatus(downloadItem.id, 'downloading', {
                download_started_at: new Date().toISOString()
            });

            // Update session current file
            await this.updateSessionCurrentFile(downloadItem.user_email, downloadItem.file_name);

            console.log(`📥 Downloading: ${downloadItem.file_name} (${downloadItem.user_email})`);

            // Tạo thư mục user nếu chưa tồn tại
            const userFolder = this.sanitizeFileName(downloadItem.user_email);
            const userPath = path.join(this.sessionConfig.download_path, userFolder);

            if (!fs.existsSync(userPath)) {
                fs.mkdirSync(userPath, { recursive: true });
            }

            // Xử lý folder
            if (downloadItem.is_folder) {
                await this.createFolder(downloadItem, userPath);
                return;
            }

            // Download file
            await this.downloadFileContent(downloadItem, userPath);

            // Update success status
            const duration = Date.now() - startTime;
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                download_completed_at: new Date().toISOString(),
                download_duration: duration
            });

            this.stats.downloadedFiles++;
            this.stats.downloadedSize += downloadItem.file_size;

            console.log(`✅ Downloaded: ${downloadItem.file_name} (${duration}ms)`);

        } catch (error) {
            console.error(`❌ Failed to download ${downloadItem.file_name}:`, error.message);

            // Handle retry logic
            const newRetryCount = downloadItem.retry_count + 1;
            const maxRetries = this.sessionConfig.max_retries || 3;

            if (newRetryCount <= maxRetries) {
                console.log(`🔄 Retrying ${downloadItem.file_name} (${newRetryCount}/${maxRetries})`);
                await this.updateDownloadItemStatus(downloadItem.id, 'pending', {
                    retry_count: newRetryCount,
                    error_message: error.message
                });

                // Add back to queue for retry (simplified - in real implementation, use proper queue)
                setTimeout(() => this.downloadFile(downloadItem), 2000 * newRetryCount);
            } else {
                await this.updateDownloadItemStatus(downloadItem.id, 'failed', {
                    error_message: error.message
                });
                this.stats.failedFiles++;
            }
        }
    }

    /**
     * Tạo folder
     */
    async createFolder(downloadItem, userPath) {
        try {
            // Tạo folder structure
            const folderPath = this.buildLocalPath(downloadItem.file_path, userPath);

            if (!fs.existsSync(folderPath)) {
                fs.mkdirSync(folderPath, { recursive: true });
            }

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                local_path: folderPath,
                download_completed_at: new Date().toISOString()
            });

            console.log(`📁 Created folder: ${folderPath}`);

        } catch (error) {
            throw new Error(`Failed to create folder: ${error.message}`);
        }
    }

    /**
     * Download file content
     */
    async downloadFileContent(downloadItem, userPath) {
        try {
            // Build local file path
            let localPath = this.buildLocalPath(downloadItem.file_path, userPath);

            // Add proper extension for Google Docs
            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);
            if (isGoogleDoc) {
                const exportMimeType = this.getExportMimeType(downloadItem.mime_type);
                const extension = googleDriveAPI.getFileExtension(exportMimeType);
                if (extension && !localPath.endsWith(extension)) {
                    localPath += extension;
                }
            }

            const localDir = path.dirname(localPath);

            // Tạo thư mục nếu chưa tồn tại
            if (!fs.existsSync(localDir)) {
                fs.mkdirSync(localDir, { recursive: true });
            }

            // Handle file name conflicts
            const finalPath = this.handleFileNameConflict(localPath);

            // Download file từ Google Drive
            const fileStream = await this.getFileStream(downloadItem);
            const writeStream = createWriteStream(finalPath);

            // Stream file to local
            await pipeline(fileStream, writeStream);

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: finalPath
            });

            // Update scanned_files table
            await this.supabase.getServiceClient()
                .from('scanned_files')
                .update({
                    download_status: 'downloaded',
                    local_path: finalPath,
                    downloaded_at: new Date().toISOString()
                })
                .eq('id', downloadItem.scanned_file_id);

        } catch (error) {
            throw new Error(`Failed to download file content: ${error.message}`);
        }
    }

    /**
     * Lấy file stream từ Google Drive
     */
    async getFileStream(downloadItem) {
        try {
            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);

            if (isGoogleDoc) {
                // Export Google Docs
                const exportMimeType = this.getExportMimeType(downloadItem.mime_type);
                return await googleDriveAPI.exportFile(downloadItem.user_email, downloadItem.file_id, exportMimeType);
            } else {
                // Download binary file
                return await googleDriveAPI.downloadFileStream(downloadItem.user_email, downloadItem.file_id);
            }

        } catch (error) {
            throw new Error(`Failed to get file stream: ${error.message}`);
        }
    }

    /**
     * Lấy export MIME type cho Google Docs
     */
    getExportMimeType(mimeType) {
        const exportMap = {
            'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
            'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation' // .pptx
        };

        return exportMap[mimeType] || 'application/pdf';
    }

    /**
     * Build local file path
     */
    buildLocalPath(googleDrivePath, userPath) {
        // Remove "My Drive/" prefix if exists
        let relativePath = googleDrivePath.replace(/^My Drive\//, '');

        // Sanitize path components
        const pathParts = relativePath.split('/').map(part => this.sanitizeFileName(part));

        return path.join(userPath, ...pathParts);
    }

    /**
     * Sanitize file name
     */
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Handle file name conflicts
     */
    handleFileNameConflict(filePath) {
        if (!fs.existsSync(filePath)) {
            return filePath;
        }

        const dir = path.dirname(filePath);
        const ext = path.extname(filePath);
        const name = path.basename(filePath, ext);

        let counter = 1;
        let newPath;

        do {
            newPath = path.join(dir, `${name}_${counter}${ext}`);
            counter++;
        } while (fs.existsSync(newPath));

        return newPath;
    }

    /**
     * Update download item status
     */
    async updateDownloadItemStatus(itemId, status, additionalFields = {}) {
        const updateData = { ...additionalFields };
        if (status) {
            updateData.status = status;
        }

        await this.supabase.getServiceClient()
            .from('download_items')
            .update(updateData)
            .eq('id', itemId);
    }

    /**
     * Update session current file
     */
    async updateSessionCurrentFile(userEmail, fileName) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                current_user_email: userEmail,
                current_file_name: fileName
            })
            .eq('id', this.sessionId);
    }

    /**
     * Emit progress event
     */
    async emitProgress() {
        // Update session stats
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('progress', {
            downloadedFiles: this.stats.downloadedFiles,
            failedFiles: this.stats.failedFiles,
            skippedFiles: this.stats.skippedFiles,
            downloadedSize: this.stats.downloadedSize
        });
    }

    /**
     * Complete session
     */
    async completeSession() {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'completed',
                completed_at: new Date().toISOString(),
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('completed', this.stats);
        console.log(`✅ Download session completed: ${this.sessionId}`);
    }

    /**
     * Handle worker error
     */
    async handleWorkerError(error) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'failed',
                error_message: error.message,
                completed_at: new Date().toISOString()
            })
            .eq('id', this.sessionId);
    }

    /**
     * Pause worker
     */
    async pause() {
        this.isPaused = true;
        console.log(`⏸️ Pausing download worker: ${this.sessionId}`);
    }

    /**
     * Resume worker
     */
    async resume() {
        this.isPaused = false;
        console.log(`▶️ Resuming download worker: ${this.sessionId}`);
    }

    /**
     * Cancel worker
     */
    async cancel() {
        this.isCancelled = true;
        console.log(`❌ Cancelling download worker: ${this.sessionId}`);
    }

    /**
     * Wait for resume
     */
    async waitForResume() {
        return new Promise((resolve) => {
            const checkResume = () => {
                if (!this.isPaused || this.isCancelled) {
                    resolve();
                } else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }
}
